import axios from 'axios';
import {base_url} from '../../../networking/config';

// Create an Axios instance with default settings
const api = axios.create({
  baseURL: base_url, // Base URL for the API
  headers: {
    'Content-Type': 'application/json', // Default headers for all requests
  },
});

export const fetchCategory = async () => {
  try {
    const response = await axios.get(
      'https://india-customer-care-api.apps.openxcell.dev/app/v1/category',
      {
        headers: {
          'Content-Type': 'application/json',
          // Add any other headers needed by your API (e.g., authorization tokens)
        },
      },
    );
    console.log('Data:', response.data.length); // Log the length of the response data
    return response.data; // Return the response data
  } catch (error) {
    console.log(error.response);
    throw new Error(error.response ? error.response.data : error.message); // Handle error
  }
};
