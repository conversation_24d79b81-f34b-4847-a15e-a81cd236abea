import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Linking,
} from 'react-native';
import {Images} from '../assets';
import {useNavigation} from '@react-navigation/native';
import {ContactNumber} from '../screens/companyDetails/companyDetailsScreen';

interface Props {
  numberData?: ContactNumber;
}

const CustomTollFreeNumbersCard = ({numberData}: Props) => {
  const navigation = useNavigation();

  const handlePhoneCall = (number: string) => {
    const phoneNumber = `tel:${number}`;
    Linking.openURL(phoneNumber).catch((err: Error) =>
      console.error('Failed to open phone call:', err),
    );
  };
  const formatVoteCount = (count: number): string => {
    if (count < 1000) {
      return count.toString();
    }
    return (count / 1000).toFixed(1) + 'k';
  };
  return (
    <View style={styles.card}>
      <View style={styles.titleView}>
        <Text style={[styles.titleText]}>{numberData?.description}</Text>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}>
          <Image style={styles.phoneIcon} source={Images.ic_phoneCall} />
          <TouchableOpacity
            onPress={() => handlePhoneCall(numberData?.number ?? '')}>
            <Text style={[styles.phoneNumber]}>{numberData?.number}</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.generalInfo}>
          <View style={styles.iconContainer}>
            <Image style={styles.upVoteIcon} source={Images.ic_arrowUp} />
            <Text style={styles.upVoteText}>
              {' '}
              {formatVoteCount(numberData?.upvoteCount || 0)}
            </Text>
          </View>
          <View style={styles.iconContainer}>
            <Image style={styles.upVoteIcon} source={Images.ic_arrowDown} />
            <Text style={styles.upVoteText}>
              {formatVoteCount(numberData?.downvoteCount || 0)}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'column',
    alignItems: 'stretch',
    backgroundColor: '#f1f5f9',
    borderRadius: 10,
    marginBottom: 10,
    borderWidth: 1.5,
    borderColor: '#D7E2F1',
    justifyContent: 'space-evenly',
  },
  titleText: {
    fontFamily: 'Poppins-Bold',
    fontSize: 17,
  },
  titleView: {
    margin: 10,
  },
  phoneNumber: {
    fontSize: 17,
    fontFamily: 'Poppins-Medium',
    textDecorationLine: 'underline',
    color: '#0066cc',
  },
  phoneIcon: {
    marginBottom: 3,
    marginLeft: 3,
    height: 20,
    width: 20,
    marginRight: 5,
  },
  generalInfo: {
    paddingTop: 5,
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    width: 200,
  },
  iconContainer: {
    backgroundColor: '#D9E2EF',
    padding: 4,
    borderRadius: 6,
    width: 60,
    height: 30,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  upVoteIcon: {
    marginLeft: 3,
    height: 20,
    width: 20,
    marginRight: 5,
  },
  upVoteText: {
    marginTop: 1,
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
    marginRight: 5,
  },
});

export default CustomTollFreeNumbersCard;
