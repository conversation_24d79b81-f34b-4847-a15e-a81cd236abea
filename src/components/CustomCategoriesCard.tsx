import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
  TouchableNativeFeedback,
} from 'react-native';
import {Images} from '../assets';
import {CategoryData} from '../database/watermelon/repositories/categoryRepository';
import {useAppNavigation} from '../hooks/useAppNavigation';
import {createImageErrorHandler, getSafeImageSource} from '../utils/imageUtils';

interface Props {
  item: CategoryData;
}

const CustomCategoriesCard = ({item}: Props) => {
  const navigation = useAppNavigation();

  const [imageSource, setImageSource] = useState(() => {
    return getSafeImageSource(item.iconUrl, Images.defaultIcon);
  });

  const handlePress = () => {
    console.log(
      'Navigating to CompanyScreen with apiCategoryId:',
      item.apiCategoryId,
    );
    navigation.navigate('CompanyScreen', {
      categoryId: item.apiCategoryId || undefined, // Use original API categoryId (number)
      title: item.name,
    });
  };

  // Use TouchableNativeFeedback on Android for ripple effect, TouchableOpacity on iOS
  const Touchable =
    Platform.OS === 'android' ? TouchableNativeFeedback : TouchableOpacity;
  const touchableProps =
    Platform.OS === 'android'
      ? {
          background: TouchableNativeFeedback.Ripple('#c0c0c0', false),
          useForeground: true,
        }
      : {
          activeOpacity: 0.7,
        };

  return (
    <View style={styles.cardWrapper}>
      <Touchable onPress={handlePress} {...touchableProps}>
        <View style={styles.checkboxContainer}>
          <View style={styles.leftContainer}>
            <View style={styles.categoriesIconView}>
              <Image
                style={styles.categoriesIcon}
                source={imageSource}
                defaultSource={Images.defaultIcon}
                onError={createImageErrorHandler(
                  'CustomCategoriesCard',
                  item.iconUrl,
                  {
                    categoryName: item.name,
                    categoryId: item.apiCategoryId,
                  },
                  () => setImageSource(Images.defaultIcon),
                )}
              />
            </View>
            <Text style={styles.label}>{item.name}</Text>
          </View>
          <Image source={Images.ic_arrowRight} style={styles.rightArrowIcon} />
        </View>
      </Touchable>
    </View>
  );
};

const styles = StyleSheet.create({
  cardWrapper: {
    borderRadius: 10,
    marginBottom: 10,
    overflow: 'hidden', // Important for Android ripple effect
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f1f5f9',
    padding: 12,
    borderRadius: 10,
    borderWidth: 1.5,
    borderColor: '#D7E2F1',
    height: 68,
    justifyContent: 'space-between',
  },
  rightArrowIcon: {
    height: 26,
    width: 26,
    resizeMode: 'contain',
  },
  categoriesIconView: {
    width: 50,
    height: 50,
    padding: 5,
  },
  categoriesIcon: {
    flex: 1,
    height: '100%',
    width: '100%',
    resizeMode: 'contain',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    marginLeft: 10,
    fontSize: 17,
    fontFamily: 'Poppins-Medium',
  },
});

export default CustomCategoriesCard;
