import axios, { AxiosResponse } from 'axios';
import { CONFIG } from '../common/constant';

// Create an Axios instance with default settings for categories
const categoryApi = axios.create({
  baseURL: CONFIG.API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 40000, // 40 seconds timeout
});

// Request interceptor for logging
categoryApi.interceptors.request.use(
  (config) => {
    console.info(`
      ====================================
      ============== Request =============
      URL: ${config.baseURL}${config.url}
      Method: ${config.method?.toUpperCase()}
      Headers: ${JSON.stringify(config.headers, null, 2)}
      Data: ${JSON.stringify(config.data, null, 2)}
      ====================================
    `);
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for logging and error handling
categoryApi.interceptors.response.use(
  (response: AxiosResponse) => {
    console.info(`
      ====================================
      ============= Response =============
      Status: ${response.status}
      Data: ${JSON.stringify(response.data, null, 2)}
      ====================================
    `);
    return response;
  },
  (error) => {
    console.error('Response error:', error);
    if (error.response) {
      // Server responded with error status
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
    } else if (error.request) {
      // Request was made but no response received
      console.error('No response received:', error.request);
    } else {
      // Something else happened
      console.error('Error message:', error.message);
    }
    return Promise.reject(error);
  }
);

export interface ApiCategory {
  categoryId?: number;
  name: string;
  iconUrl?: string;
  isActive?: number;
  created_at?: string;
  updated_at?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

class CategoryApiService {
  async fetchCategories(): Promise<ApiCategory[]> {
    try {
      const response = await categoryApi.get<ApiResponse<ApiCategory[]> | ApiCategory[]>('/category');
      
      // Handle different response formats
      if (Array.isArray(response.data)) {
        return response.data;
      } else if (response.data && 'data' in response.data) {
        return response.data.data;
      } else {
        throw new Error('Unexpected response format');
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  }

  async fetchCategoryById(id: number): Promise<ApiCategory> {
    try {
      const response = await categoryApi.get<ApiResponse<ApiCategory> | ApiCategory>(`/category/${id}`);
      
      // Handle different response formats
      if ('data' in response.data) {
        return response.data.data;
      } else {
        return response.data as ApiCategory;
      }
    } catch (error) {
      console.error('Error fetching category by id:', error);
      throw error;
    }
  }

  async createCategory(category: Omit<ApiCategory, 'categoryId' | 'created_at' | 'updated_at'>): Promise<ApiCategory> {
    try {
      const response = await categoryApi.post<ApiResponse<ApiCategory> | ApiCategory>('/category', category);
      
      // Handle different response formats
      if ('data' in response.data) {
        return response.data.data;
      } else {
        return response.data as ApiCategory;
      }
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  }

  async updateCategory(id: number, category: Partial<ApiCategory>): Promise<ApiCategory> {
    try {
      const response = await categoryApi.put<ApiResponse<ApiCategory> | ApiCategory>(`/category/${id}`, category);
      
      // Handle different response formats
      if ('data' in response.data) {
        return response.data.data;
      } else {
        return response.data as ApiCategory;
      }
    } catch (error) {
      console.error('Error updating category:', error);
      throw error;
    }
  }

  async deleteCategory(id: number): Promise<boolean> {
    try {
      await categoryApi.delete(`/category/${id}`);
      return true;
    } catch (error) {
      console.error('Error deleting category:', error);
      throw error;
    }
  }
}

export default new CategoryApiService();
