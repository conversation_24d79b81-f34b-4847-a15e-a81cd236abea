import { AppState, AppStateStatus } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import categoryApiService from './categoryApiService';
import companyApiService from './companyApiService';
import companyCategoryApiService from './companyCategoryApiService';
import syncStateRepository, { SyncStateData } from '../database/watermelon/repositories/syncStateRepository';
import watermelonCategoryRepository from '../database/watermelon/repositories/categoryRepository';
import watermelonCompanyRepository, { CompanyData } from '../database/watermelon/repositories/companyRepository';
import watermelonCompanyCategoryRepository, { CompanyCategoryData } from '../database/watermelon/repositories/companyCategoryRepository';
import watermelonNumberRepository, { NumberData } from '../database/watermelon/repositories/numberRepository';
import { extractFirstAvailableNumber } from '../utils/numberUtils';

export type SyncKey = 'categories' | 'companies' | 'company_categories';

interface SyncTask {
  key: SyncKey;
  execute: () => Promise<void>;
  maxRetries: number;
}

class BackgroundSyncManager {
  private isInitialized = false;
  private isNetworkConnected = true;
  private appState: AppStateStatus = 'active';
  private syncTasks: Map<SyncKey, SyncTask> = new Map();
  private activeSyncs: Set<SyncKey> = new Set();
  private appStateSubscription: any = null;

  constructor() {
    this.setupSyncTasks();
  }

  private setupSyncTasks() {
    this.syncTasks.set('categories', {
      key: 'categories',
      execute: this.syncCategories.bind(this),
      maxRetries: 3,
    });

    this.syncTasks.set('companies', {
      key: 'companies',
      execute: this.syncCompanies.bind(this),
      maxRetries: 3,
    });

    this.syncTasks.set('company_categories', {
      key: 'company_categories',
      execute: this.syncCompanyCategories.bind(this),
      maxRetries: 3,
    });
  }

  async initialize() {
    if (this.isInitialized) {
      return;
    }

    console.log('[BackgroundSync] Initializing...');

    // Setup network listener
    NetInfo.addEventListener(state => {
      const wasConnected = this.isNetworkConnected;
      this.isNetworkConnected = state.isConnected ?? false;

      if (!wasConnected && this.isNetworkConnected) {
        console.log('[BackgroundSync] Network reconnected, resuming syncs...');
        this.resumePendingSyncs();
      }
    });

    // Setup app state listener
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange.bind(this));

    // Get initial network state
    const netState = await NetInfo.fetch();
    this.isNetworkConnected = netState.isConnected ?? false;

    this.isInitialized = true;

    // Start initial sync if network is available
    if (this.isNetworkConnected) {
      this.startBackgroundSync();
    }

    // Resume any pending syncs from previous app sessions
    this.resumePendingSyncs();
  }

  private handleAppStateChange(nextAppState: AppStateStatus) {
    const previousAppState = this.appState;
    this.appState = nextAppState;

    if (previousAppState === 'background' && nextAppState === 'active') {
      console.log('[BackgroundSync] App became active, resuming syncs...');
      this.resumePendingSyncs();
    }
  }

  async startBackgroundSync() {
    if (!this.isNetworkConnected) {
      console.log('[BackgroundSync] No network connection, skipping sync');
      return;
    }

    console.log('[BackgroundSync] Starting background sync...');

    // Check for stuck syncs and reset them
    await this.checkAndResetStuckSyncs();

    // Initialize sync states for all tasks
    await this.initializeSyncState('categories');
    await this.initializeSyncState('companies');
    await this.initializeSyncState('company_categories');

    // Start syncing in background (don't await)
    this.executeSyncTask('categories');
    this.executeSyncTask('companies');
    this.executeSyncTask('company_categories');
  }

  private async initializeSyncState(syncKey: SyncKey) {
    const existingState = await syncStateRepository.getBySyncKey(syncKey);

    // Special handling for categories - check if sync is needed before setting to pending
    if (syncKey === 'categories') {
      const shouldSync = await this.shouldSyncCategories();
      if (!shouldSync) {
        // Mark as completed if sync is not needed
        await syncStateRepository.createOrUpdate({
          syncKey,
          status: 'completed',
          progress: 100,
          retryCount: 0,
        });
        return;
      }
    }

    // Special handling for companies - check if sync is needed before setting to pending
    if (syncKey === 'companies') {
      const shouldSync = await this.shouldSyncCompanies();
      if (!shouldSync) {
        // Mark as completed if sync is not needed
        await syncStateRepository.createOrUpdate({
          syncKey,
          status: 'completed',
          progress: 100,
          retryCount: 0,
        });
        return;
      }
    }

    // Special handling for company_categories - check if sync is needed before setting to pending
    if (syncKey === 'company_categories') {
      const shouldSync = await this.shouldSyncCompanyCategories();
      if (!shouldSync) {
        // Mark as completed if sync is not needed
        await syncStateRepository.createOrUpdate({
          syncKey,
          status: 'completed',
          progress: 100,
          retryCount: 0,
        });
        return;
      }
    }

    if (!existingState || existingState.status === 'completed') {
      await syncStateRepository.createOrUpdate({
        syncKey,
        status: 'pending',
        progress: 0,
        retryCount: 0,
      });
    }
  }

  private async shouldSyncCategories(): Promise<boolean> {
    try {
      // Check if categories exist locally
      const categoryCount = await watermelonCategoryRepository.getCount();

      if (categoryCount === 0) {
        console.log('[BackgroundSync] No categories found locally, sync needed');
        return true;
      }

      // Check if a week has passed since last successful sync
      const syncState = await syncStateRepository.getBySyncKey('categories');
      if (!syncState || !syncState.lastSyncAt) {
        console.log('[BackgroundSync] No previous categories sync found, sync needed');
        return true;
      }

      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      if (syncState.lastSyncAt < oneWeekAgo) {
        console.log('[BackgroundSync] More than a week since last categories sync, sync needed');
        return true;
      }

      console.log('[BackgroundSync] Categories are up to date, skipping sync');
      return false;
    } catch (error) {
      console.error('[BackgroundSync] Error checking if categories sync is needed:', error);
      // If there's an error checking, err on the side of syncing
      return true;
    }
  }

  private async shouldSyncCompanies(): Promise<boolean> {
    try {
      // Check if companies exist locally
      const companyCount = await watermelonCompanyRepository.getCount();

      if (companyCount === 0) {
        console.log('[BackgroundSync] No companies found locally, sync needed');
        return true;
      }

      // Check if a week has passed since last successful sync
      const syncState = await syncStateRepository.getBySyncKey('companies');
      if (!syncState || !syncState.lastSyncAt) {
        console.log('[BackgroundSync] No previous sync found, sync needed');
        return true;
      }

      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      if (syncState.lastSyncAt < oneWeekAgo) {
        console.log('[BackgroundSync] More than a week since last sync, sync needed');
        return true;
      }

      console.log('[BackgroundSync] Companies are up to date, skipping sync');
      return false;
    } catch (error) {
      console.error('[BackgroundSync] Error checking if companies sync is needed:', error);
      // If there's an error checking, err on the side of syncing
      return true;
    }
  }

  private async shouldSyncCompanyCategories(): Promise<boolean> {
    try {
      // Check if company categories exist locally
      const companyCategoryCount = await watermelonCompanyCategoryRepository.getCount();

      if (companyCategoryCount === 0) {
        console.log('[BackgroundSync] No company categories found locally, sync needed');
        return true;
      }

      // Check if a week has passed since last successful sync
      const syncState = await syncStateRepository.getBySyncKey('company_categories');
      if (!syncState || !syncState.lastSyncAt) {
        console.log('[BackgroundSync] No previous company categories sync found, sync needed');
        return true;
      }

      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      if (syncState.lastSyncAt < oneWeekAgo) {
        console.log('[BackgroundSync] More than a week since last company categories sync, sync needed');
        return true;
      }

      console.log('[BackgroundSync] Company categories are up to date, skipping sync');
      return false;
    } catch (error) {
      console.error('[BackgroundSync] Error checking if company categories sync is needed:', error);
      // If there's an error checking, err on the side of syncing
      return true;
    }
  }

  private async executeSyncTask(syncKey: SyncKey) {
    if (this.activeSyncs.has(syncKey)) {
      console.log(`[BackgroundSync] Sync already active for ${syncKey}`);
      return;
    }

    const task = this.syncTasks.get(syncKey);
    if (!task) {
      console.error(`[BackgroundSync] No task found for ${syncKey}`);
      return;
    }

    // Special handling for categories sync - check if sync is needed
    if (syncKey === 'categories') {
      const shouldSync = await this.shouldSyncCategories();
      if (!shouldSync) {
        // Mark as completed without syncing
        await syncStateRepository.createOrUpdate({
          syncKey: 'categories',
          status: 'completed',
          progress: 100,
          retryCount: 0,
        });
        return;
      }
    }

    // Special handling for companies sync - check if sync is needed
    if (syncKey === 'companies') {
      const shouldSync = await this.shouldSyncCompanies();
      if (!shouldSync) {
        // Mark as completed without syncing
        await syncStateRepository.createOrUpdate({
          syncKey: 'companies',
          status: 'completed',
          progress: 100,
          retryCount: 0,
        });
        return;
      }
    }

    // Special handling for company_categories sync - check if sync is needed
    if (syncKey === 'company_categories') {
      const shouldSync = await this.shouldSyncCompanyCategories();
      if (!shouldSync) {
        // Mark as completed without syncing
        await syncStateRepository.createOrUpdate({
          syncKey: 'company_categories',
          status: 'completed',
          progress: 100,
          retryCount: 0,
        });
        return;
      }
    }

    this.activeSyncs.add(syncKey);

    try {
      const syncState = await syncStateRepository.getBySyncKey(syncKey);
      if (!syncState || syncState.status === 'completed') {
        console.log(`[BackgroundSync] Sync already completed for ${syncKey}`);
        return;
      }

      if (syncState.status === 'in_progress') {
        console.log(`[BackgroundSync] Sync already in progress for ${syncKey}`);
        return;
      }

      // Update status to in_progress
      await syncStateRepository.createOrUpdate({
        ...syncState,
        status: 'in_progress',
        progress: 0,
        errorMessage: undefined,
      });

      console.log(`[BackgroundSync] Starting sync for ${syncKey}...`);
      await task.execute();

      // Mark as completed
      await syncStateRepository.createOrUpdate({
        ...syncState,
        status: 'completed',
        progress: 100,
        lastSyncAt: new Date(),
        errorMessage: undefined,
      });

      console.log(`[BackgroundSync] Completed sync for ${syncKey}`);

    } catch (error) {
      console.error(`[BackgroundSync] Error syncing ${syncKey}:`, error);

      const syncState = await syncStateRepository.getBySyncKey(syncKey);
      if (syncState) {
        const newRetryCount = syncState.retryCount + 1;
        const shouldRetry = newRetryCount < task.maxRetries;

        await syncStateRepository.createOrUpdate({
          ...syncState,
          status: shouldRetry ? 'pending' : 'failed',
          retryCount: newRetryCount,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        });

        if (shouldRetry) {
          console.log(`[BackgroundSync] Will retry ${syncKey} (attempt ${newRetryCount}/${task.maxRetries})`);
          // Retry after a delay
          setTimeout(() => {
            this.executeSyncTask(syncKey);
          }, 5000 * newRetryCount); // Exponential backoff
        }
      }
    } finally {
      this.activeSyncs.delete(syncKey);
    }
  }

  private async resumePendingSyncs() {
    if (!this.isNetworkConnected) {
      return;
    }

    const pendingSyncs = await syncStateRepository.getPendingOrFailedSyncs();

    for (const syncState of pendingSyncs) {
      if (this.syncTasks.has(syncState.syncKey as SyncKey)) {
        console.log(`[BackgroundSync] Resuming sync for ${syncState.syncKey}`);
        this.executeSyncTask(syncState.syncKey as SyncKey);
      }
    }
  }

  private async syncCategories() {
    console.log('[BackgroundSync] Syncing categories...');

    // First, check if we already have categories in local database
    const localCategories = await watermelonCategoryRepository.getAll();
    console.log(`[BackgroundSync] Found ${localCategories.length} categories in local database`);

    // If we have local categories, check if they're recent enough (within 1 week)
    if (localCategories.length > 0) {
      const syncState = await syncStateRepository.getBySyncKey('categories');
      const lastSyncAt = syncState?.lastSyncAt;

      console.log(`[BackgroundSync] Categories sync state:`, {
        exists: !!syncState,
        status: syncState?.status,
        lastSyncAt: lastSyncAt?.toISOString(),
        progress: syncState?.progress
      });

      if (lastSyncAt) {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

        if (lastSyncAt > oneWeekAgo) {
          console.log(`[BackgroundSync] Categories were last synced on ${lastSyncAt.toISOString()}, skipping API call (using 1-week cache)`);

          // Update progress to completed without API call
          if (syncState) {
            await syncStateRepository.createOrUpdate({
              ...syncState,
              progress: 100,
              status: 'completed',
            });
          }
          return;
        } else {
          console.log(`[BackgroundSync] Categories last synced on ${lastSyncAt.toISOString()}, older than 1 week, will fetch from API`);
        }
      } else {
        console.log(`[BackgroundSync] No lastSyncAt found in sync state, will fetch from API`);
      }
    }

    // Only call API if we don't have local data or it's older than 1 week
    console.log('[BackgroundSync] Local categories are missing or outdated, fetching from API...');

    const apiCategories = await categoryApiService.fetchCategories();
    console.log(`[BackgroundSync] Fetched ${apiCategories.length} categories from API`);

    // Log first category for debugging
    if (apiCategories.length > 0) {
      console.log('[BackgroundSync] Sample API category:', JSON.stringify(apiCategories[0], null, 2));
    }

    // Update progress
    const syncState = await syncStateRepository.getBySyncKey('categories');
    if (syncState) {
      await syncStateRepository.createOrUpdate({
        ...syncState,
        progress: 50,
      });
    }

    // Use batch create or update to handle duplicates properly
    const categoryDataList = apiCategories.map(apiCategory => ({
      apiCategoryId: apiCategory.categoryId, // Preserve original API categoryId (required field)
      name: apiCategory.name,
      iconUrl: apiCategory.iconUrl,
      isActive: apiCategory.isActive ? 1 : 0, // Convert truthy/falsy value to number
    }));

    console.log('[BackgroundSync] Mapped category data for database:', JSON.stringify(categoryDataList[0], null, 2));

    await watermelonCategoryRepository.batchCreateOrUpdate(categoryDataList);

    // Update sync state to completed with lastSyncAt timestamp
    const finalSyncState = await syncStateRepository.getBySyncKey('categories');
    if (finalSyncState) {
      await syncStateRepository.createOrUpdate({
        ...finalSyncState,
        status: 'completed',
        progress: 100,
        lastSyncAt: new Date(),
        errorMessage: undefined,
      });
    }

    console.log(`[BackgroundSync] ✅ Successfully synced ${apiCategories.length} categories`);
  }

  private async syncCompanies() {
    console.log('[BackgroundSync] Syncing companies with pagination...');

    try {
      // Get sync state for progress tracking
      let syncState = await syncStateRepository.getBySyncKey('companies');

      // Step 1: Clear existing companies and numbers (10% progress)
      console.log('[BackgroundSync] Clearing existing companies and numbers...');
      await watermelonCompanyRepository.clearAll();
      await watermelonNumberRepository.clearAll();

      if (syncState) {
        await syncStateRepository.createOrUpdate({
          ...syncState,
          progress: 10,
        });
      }

      // Step 2: Fetch all companies with pagination (10% - 80% progress)
      console.log('[BackgroundSync] Fetching companies with pagination...');
      const allCompanies: any[] = [];
      let currentPage = 1;
      let totalPages = 1;
      const limit = 500; // Fetch 500 records per page

      do {
        console.log(`[BackgroundSync] Fetching companies page ${currentPage}/${totalPages}...`);

        try {
          const pageResponse = await companyApiService.fetchCompaniesPage(currentPage, limit);

          // Extract companies from response
          const companies = pageResponse.data.companies;
          allCompanies.push(...companies);

          // Update pagination info from response data
          const total = pageResponse.data.total;
          totalPages = Math.ceil(total / limit);

          // Update progress (10% to 80% based on pages fetched)
          const fetchProgress = 10 + Math.floor((currentPage / totalPages) * 70);
          if (syncState) {
            syncState = await syncStateRepository.getBySyncKey('companies');
            if (syncState) {
              await syncStateRepository.createOrUpdate({
                ...syncState,
                progress: Math.min(fetchProgress, 80),
              });
            }
          }

          console.log(`[BackgroundSync] Page ${currentPage}/${totalPages} - Got ${companies.length} companies (Total: ${allCompanies.length}/${total})`);
          currentPage++;

          // Safety check to prevent infinite loops
          if (currentPage > 1000) {
            console.warn('[BackgroundSync] Safety limit reached, stopping pagination');
            break;
          }

        } catch (pageError) {
          console.error(`[BackgroundSync] Error fetching page ${currentPage}:`, pageError);

          // Update sync state with error
          if (syncState) {
            syncState = await syncStateRepository.getBySyncKey('companies');
            if (syncState) {
              await syncStateRepository.createOrUpdate({
                ...syncState,
                errorMessage: `Failed at page ${currentPage}: ${pageError instanceof Error ? pageError.message : 'Unknown error'}`,
              });
            }
          }

          throw pageError; // Re-throw to be caught by outer try-catch
        }

      } while (currentPage <= totalPages);

      console.log(`[BackgroundSync] ✅ Fetched total ${allCompanies.length} companies across ${currentPage - 1} pages`);

      // Log sample API company to check number field
      if (allCompanies.length > 0) {
        console.log('[BackgroundSync] Sample API company data:', JSON.stringify(allCompanies[0], null, 2));
        console.log('[BackgroundSync] Sample API company number field:', allCompanies[0].number);
      }

      // Step 3: Convert and save companies and numbers (80% - 100% progress)
      console.log('[BackgroundSync] Converting and saving companies and numbers to database...');

      // Extract numbers data from companies
      const allNumbers: NumberData[] = [];

      // Convert API companies to local format and extract numbers
      const companyDataList: CompanyData[] = allCompanies.map(apiCompany => {
        // Extract first available number using utility function
        const firstAvailableNumber = extractFirstAvailableNumber(apiCompany.number);

        // Handle number field - can be string (old format) or object (new format)
        if (typeof apiCompany.number === 'object' && apiCompany.number !== null) {
          // Extract numbers from new object format
          const numberObj = apiCompany.number;

          // Process TOLL_FREE numbers
          if (numberObj.TOLL_FREE && Array.isArray(numberObj.TOLL_FREE)) {
            numberObj.TOLL_FREE.forEach(num => {
              allNumbers.push({
                api_number_id: num.numberId,
                company_id: apiCompany.companyId,
                number: num.number,
                description: num.description,
                type: 'TOLL_FREE',
                upvote_count: num.upvoteCount,
                downvote_count: num.downvoteCount,
                is_whatsapp: num.isWhatsapp ? 1 : 0,
              });
            });
          }

          // Process ALL_INDIA numbers
          if (numberObj.ALL_INDIA && Array.isArray(numberObj.ALL_INDIA)) {
            numberObj.ALL_INDIA.forEach(num => {
              allNumbers.push({
                api_number_id: num.numberId,
                company_id: apiCompany.companyId,
                number: num.number,
                description: num.description,
                type: 'ALL_INDIA',
                upvote_count: num.upvoteCount,
                downvote_count: num.downvoteCount,
                is_whatsapp: num.isWhatsapp ? 1 : 0,
              });
            });
          }

          // Process INTERNATIONAL numbers
          if (numberObj.INTERNATIONAL && Array.isArray(numberObj.INTERNATIONAL)) {
            numberObj.INTERNATIONAL.forEach(num => {
              allNumbers.push({
                api_number_id: num.numberId,
                company_id: apiCompany.companyId,
                number: num.number,
                description: num.description,
                type: 'INTERNATIONAL',
                upvote_count: num.upvoteCount,
                downvote_count: num.downvoteCount,
                is_whatsapp: num.isWhatsapp ? 1 : 0,
              });
            });
          }
        }

        return {
          company_id: apiCompany.companyId || 0, // Store original API companyId
          company_name: apiCompany.companyName || '',
          parent_company: apiCompany.parentCompany || '',
          company_email: apiCompany.companyEmail || '',
          company_logo_url: apiCompany.companyLogoUrl || '',
          company_country: apiCompany.companyCountry || '',
          company_address: apiCompany.companyAddress || '',
          company_website: apiCompany.companyWebsite || '',
          number: firstAvailableNumber || '', // Use first available number from prioritized extraction
          upvote_count: apiCompany.upVoteCount || 0,
          downvote_count: apiCompany.downVoteCount || 0,
        };
      });

      // Log sample converted data
      if (companyDataList.length > 0) {
        console.log('[BackgroundSync] Sample converted company data:', JSON.stringify(companyDataList[0], null, 2));
        console.log('[BackgroundSync] Sample converted company number field:', companyDataList[0].number);
      }

      if (allNumbers.length > 0) {
        console.log(`[BackgroundSync] Extracted ${allNumbers.length} numbers from companies`);
        console.log('[BackgroundSync] Sample extracted number:', JSON.stringify(allNumbers[0], null, 2));
      }

      // Batch create companies in chunks to avoid memory issues
      const chunkSize = 250; // Increased chunk size for better performance
      for (let i = 0; i < companyDataList.length; i += chunkSize) {
        const chunk = companyDataList.slice(i, i + chunkSize);
        await watermelonCompanyRepository.batchCreate(chunk);

        // Update progress (80% to 90% for companies)
        const saveProgress = 80 + Math.floor(((i + chunk.length) / companyDataList.length) * 10);
        if (syncState) {
          syncState = await syncStateRepository.getBySyncKey('companies');
          if (syncState) {
            await syncStateRepository.createOrUpdate({
              ...syncState,
              progress: Math.min(saveProgress, 90),
            });
          }
        }

        console.log(`[BackgroundSync] Saved ${i + chunk.length}/${companyDataList.length} companies`);
      }

      // Save numbers data if available (90% to 100% progress)
      if (allNumbers.length > 0) {
        console.log(`[BackgroundSync] Saving ${allNumbers.length} numbers to database...`);
        await watermelonNumberRepository.batchCreate(allNumbers);

        // Update progress to 100%
        if (syncState) {
          syncState = await syncStateRepository.getBySyncKey('companies');
          if (syncState) {
            await syncStateRepository.createOrUpdate({
              ...syncState,
              progress: 100,
            });
          }
        }

        console.log(`[BackgroundSync] ✅ Successfully saved ${allNumbers.length} numbers`);
      } else {
        // No numbers to save, mark as 100% complete
        if (syncState) {
          syncState = await syncStateRepository.getBySyncKey('companies');
          if (syncState) {
            await syncStateRepository.createOrUpdate({
              ...syncState,
              progress: 100,
            });
          }
        }
      }

      console.log(`[BackgroundSync] ✅ Successfully synced ${allCompanies.length} companies and ${allNumbers.length} numbers`);

    } catch (error) {
      console.error('[BackgroundSync] Error syncing companies:', error);
      throw error;
    }
  }

  private async syncCompanyCategories() {
    console.log('[BackgroundSync] Syncing company categories...');

    try {
      // Get sync state for progress tracking
      let syncState = await syncStateRepository.getBySyncKey('company_categories');

      // Step 1: Clear existing company categories (10% progress)
      console.log('[BackgroundSync] Clearing existing company categories...');
      await watermelonCompanyCategoryRepository.clearAll();

      if (syncState) {
        await syncStateRepository.createOrUpdate({
          ...syncState,
          progress: 10,
        });
      }

      // Step 2: Fetch all company categories (10% - 80% progress)
      console.log('[BackgroundSync] Fetching company categories...');
      const apiCompanyCategories = await companyCategoryApiService.fetchCompanyCategories();
      console.log(`[BackgroundSync] Fetched ${apiCompanyCategories.length} company categories from API`);

      // Log first company category for debugging
      if (apiCompanyCategories.length > 0) {
        console.log('[BackgroundSync] Sample API company category:', JSON.stringify(apiCompanyCategories[0], null, 2));
      }

      // Update progress to 80%
      if (syncState) {
        syncState = await syncStateRepository.getBySyncKey('company_categories');
        if (syncState) {
          await syncStateRepository.createOrUpdate({
            ...syncState,
            progress: 80,
          });
        }
      }

      // Step 3: Convert and save company categories (80% - 100% progress)
      console.log('[BackgroundSync] Converting and saving company categories to database...');

      // Convert API company categories to local format
      const companyCategoryDataList: CompanyCategoryData[] = apiCompanyCategories.map(apiCompanyCategory => ({
        apiId: apiCompanyCategory.id,
        companyId: apiCompanyCategory.companyId,
        categoryId: apiCompanyCategory.categoryId,
      }));

      // Use batch create or update to handle duplicates properly
      await watermelonCompanyCategoryRepository.batchCreateOrUpdate(companyCategoryDataList);

      // Update progress to 100%
      if (syncState) {
        syncState = await syncStateRepository.getBySyncKey('company_categories');
        if (syncState) {
          await syncStateRepository.createOrUpdate({
            ...syncState,
            progress: 100,
          });
        }
      }

      console.log(`[BackgroundSync] ✅ Successfully synced ${apiCompanyCategories.length} company categories`);

    } catch (error) {
      console.error('[BackgroundSync] Error syncing company categories:', error);
      throw error;
    }
  }

  async getSyncStatus(syncKey: SyncKey): Promise<SyncStateData | null> {
    return await syncStateRepository.getBySyncKey(syncKey);
  }

  async resetSync(syncKey: SyncKey) {
    await syncStateRepository.createOrUpdate({
      syncKey,
      status: 'pending',
      progress: 0,
      retryCount: 0,
      errorMessage: undefined,
    });
  }

  async forceResetCategories() {
    console.log('[BackgroundSync] Force resetting categories...');
    try {
      // Force reset categories data
      await watermelonCategoryRepository.forceReset();

      // Reset sync state
      await this.resetSync('categories');

      console.log('[BackgroundSync] Categories force reset completed');
    } catch (error) {
      console.error('[BackgroundSync] Error force resetting categories:', error);
      throw error;
    }
  }

  async forceSyncCategories() {
    console.log('[BackgroundSync] Force syncing categories...');
    try {
      // Reset sync state to pending to force sync
      await syncStateRepository.createOrUpdate({
        syncKey: 'categories',
        status: 'pending',
        progress: 0,
        retryCount: 0,
        errorMessage: undefined,
      });

      // Execute the sync task
      await this.executeSyncTask('categories');

      console.log('[BackgroundSync] Categories force sync completed');
    } catch (error) {
      console.error('[BackgroundSync] Error force syncing categories:', error);
      throw error;
    }
  }

  async forceSyncCompanies() {
    console.log('[BackgroundSync] Force syncing companies...');
    try {
      // Reset sync state to pending to force sync
      await syncStateRepository.createOrUpdate({
        syncKey: 'companies',
        status: 'pending',
        progress: 0,
        retryCount: 0,
        errorMessage: undefined,
      });

      // Execute the sync task
      await this.executeSyncTask('companies');

      console.log('[BackgroundSync] Companies force sync completed');
    } catch (error) {
      console.error('[BackgroundSync] Error force syncing companies:', error);
      throw error;
    }
  }

  async forceSyncCompanyCategories() {
    console.log('[BackgroundSync] Force syncing company categories...');
    try {
      // Reset sync state to pending to force sync
      await syncStateRepository.createOrUpdate({
        syncKey: 'company_categories',
        status: 'pending',
        progress: 0,
        retryCount: 0,
        errorMessage: undefined,
      });

      // Execute the sync task
      await this.executeSyncTask('company_categories');

      console.log('[BackgroundSync] Company categories force sync completed');
    } catch (error) {
      console.error('[BackgroundSync] Error force syncing company categories:', error);
      throw error;
    }
  }

  /**
   * Check for stuck syncs and reset them
   * A sync is considered stuck if it's been in 'in_progress' status for more than 10 minutes
   */
  private async checkAndResetStuckSyncs() {
    console.log('[BackgroundSync] Checking for stuck syncs...');

    const syncKeys: SyncKey[] = ['categories', 'companies', 'company_categories'];
    const stuckThresholdMs = 10 * 60 * 1000; // 10 minutes

    for (const syncKey of syncKeys) {
      try {
        const syncState = await syncStateRepository.getBySyncKey(syncKey);

        if (syncState && syncState.status === 'in_progress') {
          const timeSinceUpdate = Date.now() - (syncState.updatedAt?.getTime() || 0);

          if (timeSinceUpdate > stuckThresholdMs) {
            console.log(`[BackgroundSync] Found stuck sync for ${syncKey}, resetting...`);

            // Reset the stuck sync to pending status
            await syncStateRepository.createOrUpdate({
              ...syncState,
              status: 'pending',
              progress: 0,
              errorMessage: 'Reset due to stuck sync (timeout)',
              retryCount: 0,
            });

            console.log(`[BackgroundSync] Reset stuck sync for ${syncKey}`);
          }
        }
      } catch (error) {
        console.error(`[BackgroundSync] Error checking stuck sync for ${syncKey}:`, error);
      }
    }
  }

  /**
   * Force restart a stuck sync immediately (for debugging)
   */
  async forceRestartStuckSync(syncKey: SyncKey) {
    console.log(`[BackgroundSync] Force restarting sync for ${syncKey}...`);

    try {
      const syncState = await syncStateRepository.getBySyncKey(syncKey);

      if (syncState) {
        // Reset the sync to pending status
        await syncStateRepository.createOrUpdate({
          ...syncState,
          status: 'pending',
          progress: 0,
          errorMessage: 'Force restarted by user',
          retryCount: 0,
        });

        // Remove from active syncs if it's there
        this.activeSyncs.delete(syncKey);

        // Execute the sync task immediately
        await this.executeSyncTask(syncKey);

        console.log(`[BackgroundSync] Force restart completed for ${syncKey}`);
      } else {
        console.log(`[BackgroundSync] No sync state found for ${syncKey}`);
      }
    } catch (error) {
      console.error(`[BackgroundSync] Error force restarting sync for ${syncKey}:`, error);
      throw error;
    }
  }

  async getLastCompanySyncDate(): Promise<Date | null> {
    try {
      const syncState = await syncStateRepository.getBySyncKey('companies');
      return syncState?.lastSyncAt || null;
    } catch (error) {
      console.error('[BackgroundSync] Error getting last company sync date:', error);
      return null;
    }
  }

  async getLastCategorySyncDate(): Promise<Date | null> {
    try {
      const syncState = await syncStateRepository.getBySyncKey('categories');
      return syncState?.lastSyncAt || null;
    } catch (error) {
      console.error('[BackgroundSync] Error getting last category sync date:', error);
      return null;
    }
  }

  async getLastCompanyCategorySyncDate(): Promise<Date | null> {
    try {
      const syncState = await syncStateRepository.getBySyncKey('company_categories');
      return syncState?.lastSyncAt || null;
    } catch (error) {
      console.error('[BackgroundSync] Error getting last company category sync date:', error);
      return null;
    }
  }

  cleanup() {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
  }
}

export default new BackgroundSyncManager();
