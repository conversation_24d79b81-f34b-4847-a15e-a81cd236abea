import { Database } from '@nozbe/watermelondb';
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite';

import schema from './schema';
import migrations from './migrations';
import Category from './models/Category';
import Company from './models/Company';
import CompanyCategory from './models/CompanyCategory';
import History from './models/History';
import Number from './models/Number';
import SyncState from './models/SyncState';

// First, create the adapter to the underlying database:
const adapter = new SQLiteAdapter({
  schema,
  migrations,
  // (You might want to comment it out for production)
  onSetUpError: error => {
    console.error('WatermelonDB setup error:', error);
  }
});

// Then, make a Watermelon database from it!
const database = new Database({
  adapter,
  modelClasses: [
    Category,
    Company,
    CompanyCategory,
    History,
    Number,
    SyncState,
  ],
});

export default database;
