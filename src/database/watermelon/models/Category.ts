import { Model } from '@nozbe/watermelondb';
import { field, date } from '@nozbe/watermelondb/decorators';

export default class Category extends Model {
  static table = 'categories';

  @field('api_category_id') apiCategoryId!: number; // Store original API categoryId
  @field('name') name!: string;
  @field('icon_url') iconUrl!: string;
  @field('is_active') isActive!: number;
  @date('created_at') createdAt!: Date;
  @date('updated_at') updatedAt!: Date;
}
