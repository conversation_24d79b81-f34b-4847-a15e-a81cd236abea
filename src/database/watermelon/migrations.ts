import { schemaMigrations, createTable, addColumns } from '@nozbe/watermelondb/Schema/migrations';

export default schemaMigrations({
  migrations: [
    // Migration from version 1 to 2: Add numbers table
    {
      toVersion: 2,
      steps: [
        createTable({
          name: 'numbers',
          columns: [
            { name: 'api_number_id', type: 'number' }, // Store original API numberId
            { name: 'company_id', type: 'number' }, // API companyId
            { name: 'number', type: 'string' },
            { name: 'description', type: 'string', isOptional: true },
            { name: 'type', type: 'string' }, // TOLL_FREE, ALL_INDIA, INTERNATIONAL
            { name: 'upvote_count', type: 'number' },
            { name: 'downvote_count', type: 'number' },
            { name: 'is_whatsapp', type: 'number' }, // 0 or 1
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
          ],
        }),
      ],
    },
  ],
});
