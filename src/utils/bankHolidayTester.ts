/**
 * Bank Holiday Tester Utility
 * 
 * This utility helps test the bank holiday detection logic
 * to ensure it correctly identifies 2nd & 4th Saturdays and national holidays.
 */

export class BankHolidayTester {
  
  // Utility functions (same as in the component)
  static getNthSaturdayOfMonth(year: number, month: number, n: number): Date {
    const firstDay = new Date(year, month, 1);
    const firstSaturday = new Date(firstDay);
    
    // Find first Saturday of the month
    const daysToSaturday = (6 - firstDay.getDay()) % 7;
    firstSaturday.setDate(1 + daysToSaturday);
    
    // Calculate nth Saturday
    const nthSaturday = new Date(firstSaturday);
    nthSaturday.setDate(firstSaturday.getDate() + (n - 1) * 7);
    
    return nthSaturday;
  }

  static isNationalHoliday(date: Date): string | null {
    const day = date.getDate();
    const month = date.getMonth() + 1; // getMonth() returns 0-11
    
    if (month === 1 && day === 26) return 'Republic Day';
    if (month === 8 && day === 15) return 'Independence Day';
    if (month === 10 && day === 2) return 'Gandhi Jayanti';
    
    return null;
  }

  static isBankHoliday(date: Date): { isHoliday: boolean; message: string } {
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();
    
    // Check for national holidays
    const nationalHoliday = this.isNationalHoliday(date);
    if (nationalHoliday) {
      return {
        isHoliday: true,
        message: `Banks are closed today for ${nationalHoliday}`
      };
    }
    
    // Check for 2nd and 4th Saturday
    const secondSaturday = this.getNthSaturdayOfMonth(year, month, 2);
    const fourthSaturday = this.getNthSaturdayOfMonth(year, month, 4);
    
    if (day === secondSaturday.getDate() && date.getDay() === 6) {
      return {
        isHoliday: true,
        message: 'Banks are closed today (2nd Saturday of the month)'
      };
    }
    
    if (day === fourthSaturday.getDate() && date.getDay() === 6) {
      return {
        isHoliday: true,
        message: 'Banks are closed today (4th Saturday of the month)'
      };
    }
    
    return { isHoliday: false, message: '' };
  }

  /**
   * Test the bank holiday logic for current month
   */
  static testCurrentMonth() {
    console.log('\n=== Bank Holiday Tester - Current Month ===\n');
    
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    
    console.log(`Testing for ${today.toLocaleDateString()}`);
    
    // Test today
    const todayResult = this.isBankHoliday(today);
    console.log(`Today is bank holiday: ${todayResult.isHoliday}`);
    if (todayResult.isHoliday) {
      console.log(`Message: ${todayResult.message}`);
    }
    
    // Show 2nd and 4th Saturdays for current month
    const secondSaturday = this.getNthSaturdayOfMonth(year, month, 2);
    const fourthSaturday = this.getNthSaturdayOfMonth(year, month, 4);
    
    console.log(`\n2nd Saturday of month: ${secondSaturday.toLocaleDateString()}`);
    console.log(`4th Saturday of month: ${fourthSaturday.toLocaleDateString()}`);
    
    // Test national holidays for current year
    const nationalHolidays = [
      new Date(year, 0, 26), // Republic Day
      new Date(year, 7, 15), // Independence Day  
      new Date(year, 9, 2),  // Gandhi Jayanti
    ];
    
    console.log('\nNational Holidays this year:');
    nationalHolidays.forEach(holiday => {
      const result = this.isBankHoliday(holiday);
      console.log(`${holiday.toLocaleDateString()}: ${result.message}`);
    });
  }

  /**
   * Test specific date
   */
  static testDate(dateString: string) {
    console.log(`\n=== Testing Date: ${dateString} ===\n`);
    
    const testDate = new Date(dateString);
    const result = this.isBankHoliday(testDate);
    
    console.log(`Date: ${testDate.toLocaleDateString()}`);
    console.log(`Day of week: ${testDate.toLocaleDateString('en-US', { weekday: 'long' })}`);
    console.log(`Is bank holiday: ${result.isHoliday}`);
    if (result.isHoliday) {
      console.log(`Message: ${result.message}`);
    }
  }

  /**
   * Test multiple dates
   */
  static testMultipleDates() {
    console.log('\n=== Testing Multiple Dates ===\n');
    
    // Test some known dates
    const testDates = [
      '2024-01-26', // Republic Day
      '2024-08-15', // Independence Day
      '2024-10-02', // Gandhi Jayanti
      '2024-01-13', // Example 2nd Saturday
      '2024-01-27', // Example 4th Saturday
      '2024-01-20', // Regular Saturday (3rd)
      '2024-01-15', // Regular Monday
    ];
    
    testDates.forEach(dateString => {
      const testDate = new Date(dateString);
      const result = this.isBankHoliday(testDate);
      
      console.log(`${testDate.toLocaleDateString()} (${testDate.toLocaleDateString('en-US', { weekday: 'short' })}): ${result.isHoliday ? result.message : 'Not a bank holiday'}`);
    });
  }
}

export default BankHolidayTester;
