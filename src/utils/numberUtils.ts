import { ApiNumber } from '../services/companyApiService';

/**
 * Extract the first available phone number from the new nested object structure
 * Priority: TOLL_FREE -> ALL_INDIA -> INTERNATIONAL
 * 
 * @param numberField - Can be string (old format) or object (new format)
 * @returns First available phone number as string, or null if none found
 */
export const extractFirstAvailableNumber = (
  numberField?: string | {
    TOLL_FREE?: ApiNumber[];
    ALL_INDIA?: ApiNumber[];
    INTERNATIONAL?: ApiNumber[];
  } | null
): string | null => {
  // Handle null/undefined
  if (!numberField) {
    return null;
  }

  // Handle old string format (backward compatibility)
  if (typeof numberField === 'string') {
    return numberField.trim() || null;
  }

  // Handle new object format
  if (typeof numberField === 'object') {
    // Priority 1: TOLL_FREE
    if (numberField.TOLL_FREE && numberField.TOLL_FREE.length > 0) {
      const firstTollFree = numberField.TOLL_FREE[0];
      if (firstTollFree.number) {
        return firstTollFree.number.trim();
      }
    }

    // Priority 2: ALL_INDIA
    if (numberField.ALL_INDIA && numberField.ALL_INDIA.length > 0) {
      const firstAllIndia = numberField.ALL_INDIA[0];
      if (firstAllIndia.number) {
        return firstAllIndia.number.trim();
      }
    }

    // Priority 3: INTERNATIONAL
    if (numberField.INTERNATIONAL && numberField.INTERNATIONAL.length > 0) {
      const firstInternational = numberField.INTERNATIONAL[0];
      if (firstInternational.number) {
        return firstInternational.number.trim();
      }
    }
  }

  return null;
};

/**
 * Extract the first available number from local database numbers
 * Priority: TOLL_FREE -> ALL_INDIA -> INTERNATIONAL
 * 
 * @param localNumbers - Grouped numbers from local database
 * @returns First available phone number as string, or null if none found
 */
export const extractFirstAvailableLocalNumber = (localNumbers: {
  TOLL_FREE: any[];
  ALL_INDIA: any[];
  INTERNATIONAL: any[];
}): string | null => {
  // Priority 1: TOLL_FREE
  if (localNumbers.TOLL_FREE && localNumbers.TOLL_FREE.length > 0) {
    const firstTollFree = localNumbers.TOLL_FREE[0];
    if (firstTollFree.number) {
      return firstTollFree.number.trim();
    }
  }

  // Priority 2: ALL_INDIA
  if (localNumbers.ALL_INDIA && localNumbers.ALL_INDIA.length > 0) {
    const firstAllIndia = localNumbers.ALL_INDIA[0];
    if (firstAllIndia.number) {
      return firstAllIndia.number.trim();
    }
  }

  // Priority 3: INTERNATIONAL
  if (localNumbers.INTERNATIONAL && localNumbers.INTERNATIONAL.length > 0) {
    const firstInternational = localNumbers.INTERNATIONAL[0];
    if (firstInternational.number) {
      return firstInternational.number.trim();
    }
  }

  return null;
};

/**
 * Get the type of the first available number
 * Priority: TOLL_FREE -> ALL_INDIA -> INTERNATIONAL
 * 
 * @param numberField - Can be string (old format) or object (new format)
 * @returns Type of the first available number, or null if none found
 */
export const getFirstAvailableNumberType = (
  numberField?: string | {
    TOLL_FREE?: ApiNumber[];
    ALL_INDIA?: ApiNumber[];
    INTERNATIONAL?: ApiNumber[];
  } | null
): string | null => {
  // Handle null/undefined
  if (!numberField) {
    return null;
  }

  // Handle old string format (backward compatibility)
  if (typeof numberField === 'string') {
    return 'LEGACY'; // Indicate this is from old format
  }

  // Handle new object format
  if (typeof numberField === 'object') {
    // Priority 1: TOLL_FREE
    if (numberField.TOLL_FREE && numberField.TOLL_FREE.length > 0) {
      return 'TOLL_FREE';
    }

    // Priority 2: ALL_INDIA
    if (numberField.ALL_INDIA && numberField.ALL_INDIA.length > 0) {
      return 'ALL_INDIA';
    }

    // Priority 3: INTERNATIONAL
    if (numberField.INTERNATIONAL && numberField.INTERNATIONAL.length > 0) {
      return 'INTERNATIONAL';
    }
  }

  return null;
};
