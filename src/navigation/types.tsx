import {NativeStackNavigationProp} from '@react-navigation/native-stack';

// Define parameter types for the Stack Navigator
export type RootStackParamList = {
  Info: undefined;
  Categories: undefined;
  CompanyScreen: {categoryId: number | undefined; title: string};
  CompanyDetailsScreen: {
    companyId?: number;
    title?: string;
    fromHistory?: boolean;
  };
};

// Define the type for navigation props that can access stack screens
export type AppStackNavigationProp =
  NativeStackNavigationProp<RootStackParamList>;
