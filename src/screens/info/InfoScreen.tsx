import React, {useState} from 'react';
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  useColorScheme,
  View,
  Dimensions,
  Image,
  TouchableOpacity,
} from 'react-native';

import {useAppNavigation} from '../../hooks/useAppNavigation';
import {Images} from '../../assets';
import appStateService from '../../services/appStateService';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

const Info = () => {
  const navigation = useAppNavigation();
  const isDarkMode = useColorScheme() === 'dark';
  const [scrollEnabled, setScrollEnabled] = useState(false);
  const handleContentSizeChange = (
    _contentWidth: number,
    contentHeight: number,
  ) => {
    // Enable scroll only if content is taller than the screen
    setScrollEnabled(contentHeight > screenHeight);
  };
  const handleGetStarted = async () => {
    try {
      // Mark first launch as completed
      await appStateService.markFirstLaunchCompleted();
      console.log('[InfoScreen] First launch marked as completed');

      // Navigate to Categories
      navigation.navigate('Categories');
    } catch (error) {
      console.error(
        '[InfoScreen] Error marking first launch completed:',
        error,
      );
      // Still navigate even if marking fails
      navigation.navigate('Categories');
    }
  };

  const backgroundStyle = {
    backgroundColor: isDarkMode ? 'clear' : 'clear',
  };

  return (
    <SafeAreaView style={[styles.safeArea, backgroundStyle]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <ScrollView
        style={backgroundStyle}
        scrollEnabled={scrollEnabled}
        onContentSizeChange={handleContentSizeChange}
        contentContainerStyle={styles.contentContainer}>
        <Image source={Images.homeScreenLogo} style={styles.image} />
        <View
          style={{
            paddingLeft: screenWidth * 0.05,
            paddingRight: screenWidth * 0.05,
          }}>
          <Text
            style={{
              fontSize: 37,
              fontFamily: 'Poppins-SemiBold',
              textAlign: 'center',
              lineHeight: 44,
              flexShrink: 1,
              flexWrap: 'nowrap',
            }}>
            Customer Care
          </Text>
          <Text
            style={{
              fontSize: 37,
              fontFamily: 'Poppins-SemiBold',
              textAlign: 'center',
              lineHeight: 40,
              flexWrap: 'nowrap',
            }}>
            Helpline Numbers
          </Text>
          <Text
            style={{
              fontSize: 18,
              textAlign: 'center',
              paddingTop: screenWidth * 0.06,
              paddingBottom: screenWidth * 0.06,
              fontFamily: 'Poppins-Regular',
            }}>
            Get quick support for banking, telecom, e-commerce, airlines, and
            more with essential customer care helpline numbers.
          </Text>
          <TouchableOpacity style={styles.button} onPress={handleGetStarted}>
            <Text style={styles.buttonText}>Get Started</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
  },
  highlight: {
    fontWeight: '700',
  },
  image: {
    width: screenWidth - 40,
    height: screenHeight * 0.52,
    resizeMode: 'contain', // or 'cover', 'stretch', etc.
    marginRight: 20,
    marginLeft: 20,
  },
  button: {
    backgroundColor: '#03194D',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    elevation: 5, // Elevation for Android
    height: 52,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    textAlign: 'center',
    fontFamily: 'Poppins-Regular',
  },
});

export default Info;
