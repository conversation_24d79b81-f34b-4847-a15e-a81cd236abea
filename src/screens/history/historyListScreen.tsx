import React, {useState, useCallback} from 'react';
import {
  StyleSheet,
  FlatList,
  SafeAreaView,
  View,
  Text,
  ActivityIndicator,
} from 'react-native';
import CustomCategoriesDetailCard from '../../components/CustomCategoriesDetailCard';
import CustomSearchBar from '../../components/CustomSearchBar';
import historyRepository, {
  HistoryWithCompanyDetails,
} from '../../database/watermelon/repositories/historyRepository';
import {useNetworkState} from '../../utils/networkStateManager';
import {useFocusEffect} from '@react-navigation/native';
import {debounce} from 'lodash';

const HistoryListScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [originalHistoryItems, setOriginalHistoryItems] = useState<
    HistoryWithCompanyDetails[]
  >([]);
  const [filteredItems, setFilteredItems] = useState<
    HistoryWithCompanyDetails[]
  >([]);
  const {isConnected} = useNetworkState();

  // Load history data
  const loadHistoryData = async () => {
    try {
      setLoading(true);
      const history = await historyRepository.getHistoryWithCompanyDetails();
      console.log('History items count:', history.length);
      console.log('History:', history);

      // Store the original list for search filtering
      setOriginalHistoryItems(history);

      // Apply search filter if query exists
      if (searchQuery && searchQuery.trim() !== '') {
        const filtered = history.filter(item => {
          // If company data is available, search by name
          if (item.company && item.company.company_name) {
            return item.company.company_name
              .toLowerCase()
              .includes(searchQuery.toLowerCase());
          }
          // If no company data, include if search matches "Company #ID"
          const defaultName = `Company #${item.company_id}`;
          return defaultName.toLowerCase().includes(searchQuery.toLowerCase());
        });
        setFilteredItems(filtered);
      } else {
        setFilteredItems(history);
      }

      // Log how many items have valid company data
      const validItemsCount = history.filter(
        item => item.company !== null,
      ).length;
      console.log(
        `Items with valid company data: ${validItemsCount}/${history.length}`,
      );
    } catch (error) {
      console.error('Error loading history:', error);
    } finally {
      setLoading(false);
    }
  };

  // Use useFocusEffect to reload data when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('History screen focused - reloading data');
      loadHistoryData();

      // Return a cleanup function (optional)
      return () => {
        // This runs when the screen is unfocused
        console.log('History screen unfocused');
      };
    }, [isConnected]),
  );

  // Filter search data function
  const filterSearchedData = (searchText: string) => {
    console.log('Filtering history with search text:', searchText);
    console.log('Original history list:', originalHistoryItems);

    if (!searchText || searchText.trim() === '') {
      // Restore the original list if search text is empty
      console.log('Restoring original history list');
      setFilteredItems([...originalHistoryItems]); // Ensure a new array reference
      return;
    }

    // Filter history items based on the search text (case-insensitive)
    const filteredHistory = originalHistoryItems.filter(item => {
      // If company data is available, search by name
      if (item.company && item.company.company_name) {
        return item.company.company_name
          .toLowerCase()
          .includes(searchText.toLowerCase());
      }
      // If no company data, include if search matches "Company #ID"
      const defaultName = `Company #${item.company_id}`;
      return defaultName.toLowerCase().includes(searchText.toLowerCase());
    });

    setFilteredItems(filteredHistory);
  };

  const debouncedSearch = useCallback(
    debounce((text: string) => {
      console.log('Debounced search text:', text);
      filterSearchedData(text);
    }, 300),
    [originalHistoryItems],
  );

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    debouncedSearch(text);
  };

  return (
    <SafeAreaView style={styles.container}>
      <CustomSearchBar
        onSearch={handleSearchChange}
        onVoiceResult={result => {
          setSearchQuery(result);
          handleSearchChange(result);
        }}
        initialValue={searchQuery}
        placeholder="Search history"
        showVoiceSearch={true}
      />

      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={styles.loaderText}>Loading history...</Text>
        </View>
      ) : filteredItems.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No history found</Text>
        </View>
      ) : (
        <FlatList
          style={{padding: 15}}
          contentContainerStyle={{paddingBottom: 30}}
          data={filteredItems}
          keyExtractor={item => item.id?.toString() || ''}
          renderItem={({item}) => {
            // If we don't have company details, just show a placeholder with the company ID
            const defaultCompanyName = `Company #${item.company_id}`;

            return (
              <CustomCategoriesDetailCard
                companyData={{
                  companyId: parseInt(item.company_id),
                  companyName: item.company?.company_name || defaultCompanyName,
                  companyLogoUrl: item.company?.company_logo_url || '',
                  parentCompany: item.company?.parent_company || null,
                  companyEmail: item.company?.company_email || null,
                  companyCountry: item.company?.company_country || null,
                  companyAddress: item.company?.company_address || null,
                  companyWebsite: item.company?.company_website || null,
                  number: item.company?.number || null, // Add phone number
                  upVoteCount: item.company?.upvote_count || 0,
                  downVoteCount: item.company?.downvote_count || 0,
                  createdAt: item.company?.created_at?.toString() || '',
                  fromHistory: true,
                }}
              />
            );
          }}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loaderText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
    fontFamily: 'Poppins-Regular',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    fontFamily: 'Poppins-Medium',
    textAlign: 'center',
  },
});

export default HistoryListScreen;
