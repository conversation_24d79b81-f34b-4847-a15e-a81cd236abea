import React, {useState} from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  Image,
  FlatList,
  TextInput,
} from 'react-native';
import {Text, Button} from 'react-native-paper';
import {Images} from '../../assets';
import {COLORS, FONTS} from '../../common/constant';
import commonStyles from '../../common/commonStyles';

interface CustomModalProps {
  visible: boolean;
  title: string;
  content: string;
  onClose: () => void;
}
const NoteModalScreen = ({
  visible,
  title,
  content,
  onClose,
}: CustomModalProps) => {
  const [selctCategoriesList, setSelctCategoriesList] = useState([]);
  const [inputValue, setInputValue] = useState('');

  const handleSave = () => {
    if (inputValue.trim() !== '') {
      setSelctCategoriesList(prevList => [
        ...prevList,
        {id: Date.now().toString(), details: inputValue},
      ]);
      setInputValue(''); // Clear the input field
    }
  };

  const toggleModal = () => {
    console.log('Note saved:', inputValue);
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <TouchableOpacity
            style={{height: 44, alignItems: 'flex-end'}}
            onPress={onClose}>
            <Image style={styles.closeIcon} source={Images.ic_close} />
          </TouchableOpacity>
          <FlatList
            data={selctCategoriesList}
            keyExtractor={item => item.id}
            renderItem={({item}) => (
              <View style={styles.itemContainer}>
                <Text style={styles.itemText}>{item.details}</Text>
              </View>
            )}
            style={{padding: 2}}
          />
          <View style={commonStyles.textHereInputView}>
            <Text style={commonStyles.textHereInputHeader}>Note</Text>
            <TextInput
              style={commonStyles.textHereView}
              multiline
              placeholder="text here..."
              placeholderTextColor={'#B7B7B7'}
              value={inputValue}
              onChangeText={setInputValue}
              textAlignVertical="top"
            />
            <Button
              mode="contained"
              onPress={handleSave}
              style={commonStyles.bottomButton}
              labelStyle={commonStyles.bottomButtonLabel}
              buttonColor="#0a1d50">
              Save
            </Button>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 10,
    maxHeight: '95%',
  },
  itemTitle: {
    fontSize: 20,
    fontFamily: FONTS.POPPINS.MEDIUM,
    marginBottom: 5,
    marginTop: 5,
  },
  itemText: {
    fontSize: 15,
    fontFamily: FONTS.POPPINS.REGULAR,
    textAlign: 'center',
  },
  itemContainer: {
    padding: 10,
    borderWidth: 1,
    borderColor: COLORS.BORDER_COLOR,
    marginBottom: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  closeIcon: {
    marginLeft: 3,
    height: 30,
    width: 30,
    marginRight: 5,
  },
});

export default NoteModalScreen;
