import React, {useState} from 'react';
import {
  StyleSheet,
  SafeAreaView,
  TextInput,
  View,
  ScrollView,
} from 'react-native';
import {Text, Button} from 'react-native-paper';
import {FONTS} from '../../common/constant';
import {COLORS} from '../../common/constant';
import commonStyles from '../../common/commonStyles';

const ReportScreen = () => {
  const [name, setName] = useState('');
  const [text, setText] = useState('');
  const [email, setEmail] = useState('');

  const handleSaveReportSelection = () => {
    // Handle save action here
    console.log('Report submitted:', {name, text});
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={[commonStyles.textHereInputView, styles.textInputView]}>
            <Text style={commonStyles.textHereInputHeader}>Name</Text>
            <TextInput
              style={styles.textInput}
              placeholder=""
              placeholderTextColor={'#B7B7B7'}
              value={name}
              onChangeText={setName}
            />
          </View>
          <View style={[commonStyles.textHereInputView, styles.textInputView]}>
            <Text style={commonStyles.textHereInputHeader}>Email</Text>
            <TextInput
              style={styles.textInput}
              placeholder=""
              placeholderTextColor={'#B7B7B7'}
              value={email}
              onChangeText={setEmail}
              textAlignVertical="top"
            />
          </View>
          <View style={[commonStyles.textHereInputView, styles.textInputView]}>
            <Text style={commonStyles.textHereInputHeader}>Comment</Text>
            <TextInput
              style={commonStyles.textHereView}
              multiline
              placeholder="text here..."
              value={text}
              onChangeText={setText}
              textAlignVertical="top"
            />
          </View>
        </ScrollView>
        <Button
          mode="contained"
          onPress={handleSaveReportSelection}
          style={styles.saveButton}
          labelStyle={styles.saveButtonLabel}
          buttonColor={'#0a1d50'}>
          Submit
        </Button>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  scrollContent: {
    paddingBottom: 20,
  },
  textInputView: {marginTop: 15, marginLeft: 15, marginRight: 15},
  textInput: {
    marginTop: 5,
    fontFamily: FONTS.POPPINS.REGULAR,
    fontSize: 17,
    height: 48,
    padding: 10,
    color: COLORS.BLACK,
    borderColor: COLORS.BORDER_COLOR,
    borderWidth: 1,
    borderRadius: 10,
  },
  saveButton: {
    marginLeft: 15,
    marginRight: 15,
    marginTop: 20,
    marginBottom: 35,
    paddingVertical: 6,
    borderRadius: 10,
  },
  saveButtonLabel: {
    fontSize: 16,
    color: '#fff',
    fontFamily: FONTS.POPPINS.MEDIUM,
  },
});

export default ReportScreen;
