# Database Schema Error Troubleshooting

## Error Description

```
Error batch creating companies: [TypeError: Cannot read property 'type' of undefined]
[BackgroundSync] Error syncing companies: [TypeError: Cannot read property 'type' of undefined]
```

## Root Cause

This error occurs when there's a mismatch between the WatermelonDB model definition and the actual database schema. Common causes include:

1. **Database created with older schema version** - The database was created before recent schema changes
2. **Migration not executed** - Database migrations didn't run properly
3. **Schema mismatch** - Model fields don't match database columns
4. **Corrupted database state** - Database is in an inconsistent state

## Immediate Solution

### Step 1: Run Troubleshooting
```typescript
import WeeklySyncTester from '../utils/testWeeklySync';

// Run comprehensive troubleshooting
await WeeklySyncTester.troubleshootDatabaseIssues();
```

### Step 2: Reset Database (Recommended)
```typescript
// Reset entire database and start fresh
await WeeklySyncTester.resetDatabaseForSchemaFix();
```

### Step 3: Alternative - Reset Only Company Data
```typescript
// Reset only company-related data (if other data is working)
await WeeklySyncTester.resetCompanyDataForSchemaFix();
```

## Detailed Troubleshooting Steps

### 1. Check Database Integrity
```typescript
// Check if all tables exist and have correct structure
await WeeklySyncTester.checkDatabaseIntegrity();
```

**Expected Output:**
```
✅ Table 'categories': X records
✅ Table 'companies': X records  
✅ Table 'company_categories': X records
✅ Table 'history': X records
✅ Table 'sync_state': X records
✅ Company schema validation passed
```

**Error Indicators:**
```
❌ Table 'companies': Error - [specific error]
❌ Company schema validation failed: Cannot read property 'type' of undefined
```

### 2. Get Database Information
```typescript
// Get detailed information about current database state
await WeeklySyncTester.getDatabaseInfo();
```

**Look for:**
- Missing fields in company records
- Undefined values where they shouldn't be
- Schema version mismatches

### 3. Check Current Schema vs Expected

**Current Schema (Version 6):**
```sql
companies:
- company_id (number)
- company_name (string)
- parent_company (string, optional)
- company_email (string, optional)
- company_logo_url (string, optional)
- company_country (string, optional)
- company_address (string, optional)
- company_website (string, optional)
- number (string, optional) ← Recently added
- upvote_count (number)
- downvote_count (number)
- created_at (number)
- updated_at (number)
```

## Resolution Methods

### Method 1: Complete Database Reset (Recommended)

**When to use:** When multiple tables have issues or you want a clean start

```typescript
// 1. Reset database
await WeeklySyncTester.resetDatabaseForSchemaFix();

// 2. Restart the app
// 3. Wait for background sync to complete
// 4. Verify data is working
await WeeklySyncTester.getSyncInfo();
```

**What this does:**
- Completely clears the database
- Recreates all tables with current schema
- Starts fresh background sync
- Populates all data from API

### Method 2: Company Data Reset

**When to use:** When only company-related tables have issues

```typescript
// 1. Reset company data
await WeeklySyncTester.resetCompanyDataForSchemaFix();

// 2. Verify company sync
await WeeklySyncTester.forceSyncForTesting();
```

**What this does:**
- Clears companies, company_categories, and history tables
- Resets sync states for company-related syncs
- Forces fresh company and company-category sync
- Preserves category data

### Method 3: Manual Migration Check

**When to use:** For debugging migration issues

1. **Check current database version:**
   ```typescript
   // Look at database info to see what fields exist
   await WeeklySyncTester.getDatabaseInfo();
   ```

2. **Verify migration history:**
   - Check if `company_id` field exists in companies table
   - Check if `number` field exists in companies table
   - Verify all expected fields are present

## Prevention

### 1. Always Test Schema Changes
```typescript
// Before deploying schema changes, test:
await WeeklySyncTester.checkDatabaseIntegrity();
```

### 2. Backup Before Major Changes
```typescript
// Export important data before schema changes
const companies = await watermelonCompanyRepository.getAll();
console.log('Backup company count:', companies.length);
```

### 3. Gradual Migration Testing
- Test migrations on development devices first
- Verify each migration step works correctly
- Test with existing data, not just fresh installs

## Common Scenarios

### Scenario 1: Fresh Install
- **Expected:** Should work without issues
- **If failing:** Schema definition problem in code

### Scenario 2: Existing App Update
- **Expected:** Migration should handle schema changes
- **If failing:** Migration not running or incomplete

### Scenario 3: Development Environment
- **Expected:** Frequent schema changes should be handled
- **If failing:** Database state corrupted from multiple changes

## Verification After Fix

### 1. Test Company Creation
```typescript
// Verify company schema works
await WeeklySyncTester.checkDatabaseIntegrity();
```

### 2. Test Background Sync
```typescript
// Verify sync works end-to-end
await WeeklySyncTester.forceSyncForTesting();
await WeeklySyncTester.getSyncInfo();
```

### 3. Test UI Integration
- Navigate to Categories screen
- Select a category
- Verify companies display with phone numbers
- Test phone call functionality

## Emergency Recovery

If all else fails:

### 1. Complete App Data Reset
```typescript
// Nuclear option - reset everything
await WeeklySyncTester.resetDatabaseForSchemaFix();
```

### 2. Reinstall App
- Uninstall the app completely
- Reinstall to get fresh database
- Should work with current schema

### 3. Check for Code Issues
- Verify model definitions match schema
- Check migration files are correct
- Ensure all imports are working

## Monitoring

### Add Logging for Future Issues
```typescript
// Add to company repository methods
console.log('Creating company with fields:', Object.keys(companyData));

// Add to background sync
console.log('Company sync - sample data:', JSON.stringify(companyDataList[0], null, 2));
```

### Regular Health Checks
```typescript
// Run periodically in development
await WeeklySyncTester.troubleshootDatabaseIssues();
```

This error is typically resolved by resetting the database to ensure schema consistency. The reset utilities provided will handle this automatically and restore full functionality.
