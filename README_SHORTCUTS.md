# Android App Shortcuts Implementation

## 🎯 Overview

This implementation adds pinned app shortcuts functionality to your React Native Android application. Users can create shortcuts on their home screen that directly open specific features within your app.

## ✨ Features

- **Company List Shortcuts**: Create shortcuts for specific category company lists
- **Categories Shortcuts**: Quick access to the main categories screen  
- **Deep Linking**: Direct navigation to specific screens when shortcuts are tapped
- **Android Only**: Optimized for Android devices (hidden on iOS)
- **User-Friendly**: Simple button interface for shortcut creation

## 🚀 Quick Start

### 1. Build and Install
```bash
cd android
./gradlew assembleDebug
# Install the APK on your Android device
```

### 2. Test Shortcuts
1. Open the app on your Android device
2. Navigate to the Categories screen
3. Tap "Add Categories to Home Screen" button
4. Confirm shortcut creation when prompted
5. Go to your home screen - you'll see the new shortcut
6. Tap the shortcut to test deep linking

### 3. Test Company List Shortcuts
1. Navigate to any category (e.g., Banks)
2. Tap "Add [Category Name] to Home Screen" button
3. Confirm shortcut creation
4. Test the shortcut from your home screen

## 📱 User Experience

### Creating Shortcuts
- Tap the "Add to Home Screen" button in the app
- System shows confirmation dialog (on modern Android)
- Shortcut appears on home screen with app icon
- Success message confirms creation

### Using Shortcuts
- Tap shortcut icon on home screen
- App opens directly to the specific screen
- No need to navigate through menus

## 🔧 Technical Details

### Files Added/Modified

#### Android Native Files
- `android/app/src/main/java/com/sourcecode/AppShortcutModule.kt` - Native shortcut module
- `android/app/src/main/java/com/sourcecode/AppShortcutPackage.kt` - Package registration
- `android/app/src/main/AndroidManifest.xml` - Permissions and intent filters
- `android/app/src/main/java/com/sourcecode/MainActivity.kt` - Deep link handling
- `android/app/src/main/java/com/sourcecode/MainApplication.kt` - Module registration

#### React Native Files
- `src/services/AppShortcutService.js` - Main shortcut service
- `src/hooks/useAppShortcuts.js` - React hook for shortcuts
- `src/components/ShortcutButton.js` - UI component
- `src/components/ShortcutExample.js` - Example implementation

#### Screen Integrations
- `src/screens/categoriesDetails/categoriesDetailsScreen.tsx` - Company list shortcuts
- `src/screens/categories/CategoriesScreen.tsx` - Categories shortcuts

### Deep Link URLs
- Categories: `indiacustomercare://categories`
- Company List: `indiacustomercare://companylist?categoryId=123&title=Bank%20Companies`

## 🛠️ Development

### Adding Shortcuts to New Screens

```javascript
import { useAppShortcuts } from '../hooks/useAppShortcuts';
import ShortcutButton from '../components/ShortcutButton';

const MyScreen = ({ navigation }) => {
  const { 
    isShortcutSupported, 
    createCompanyListShortcut, 
    isLoading 
  } = useAppShortcuts(navigation);

  return (
    <View>
      {isShortcutSupported && (
        <ShortcutButton
          title="Add to Home Screen"
          onPress={() => createCompanyListShortcut(categoryId, categoryName)}
          isLoading={isLoading}
        />
      )}
    </View>
  );
};
```

### Creating Custom Shortcuts

```javascript
import AppShortcutService from '../services/AppShortcutService';

// Create custom shortcut
const createCustomShortcut = async () => {
  const shortcutId = 'my_custom_shortcut';
  const label = 'My Feature';
  const deepLink = 'indiacustomercare://myfeature?param=value';
  
  await AppShortcutService.createShortcut(shortcutId, label, deepLink);
};
```

## 🔍 Testing

### Manual Testing Checklist
- [ ] Shortcut creation works on Android 7.1+ (modern shortcuts)
- [ ] Shortcut creation works on older Android versions (legacy shortcuts)
- [ ] Deep links navigate to correct screens
- [ ] Error handling works for unsupported devices
- [ ] Shortcuts appear with correct labels and icons
- [ ] Multiple shortcuts can be created
- [ ] App handles shortcuts when closed/backgrounded

### Device Compatibility
- **Android 7.1+ (API 25+)**: Full support with user confirmation
- **Android 5.0-7.0 (API 21-24)**: Legacy support with automatic creation
- **Below Android 5.0**: Not supported (graceful degradation)

## 🐛 Troubleshooting

### Common Issues

**Shortcuts not appearing:**
- Check if device supports shortcuts
- Verify launcher compatibility
- Ensure permissions are granted

**Deep links not working:**
- Check AndroidManifest.xml intent filters
- Verify URL scheme matches implementation
- Test with `adb shell am start -W -a android.intent.action.VIEW -d "indiacustomercare://categories"`

**Build errors:**
- Ensure AppShortcutPackage is registered in MainApplication.kt
- Check Kotlin compilation settings
- Verify all imports are correct

### Debug Commands

```bash
# Test deep link manually
adb shell am start -W -a android.intent.action.VIEW -d "indiacustomercare://categories" com.sourcecode

# Check if shortcuts are created
adb shell dumpsys shortcut

# View app logs
adb logcat | grep -E "(AppShortcut|MainActivity|indiacustomercare)"
```

## 📚 Documentation

- [Complete Implementation Guide](docs/APP_SHORTCUTS_GUIDE.md)
- [Example Component](src/components/ShortcutExample.js)

## 🎉 Success!

Your React Native app now supports Android pinned shortcuts! Users can create quick access shortcuts to their favorite features and access them directly from their home screen.

The implementation is production-ready and includes proper error handling, device compatibility checks, and user-friendly interfaces.
