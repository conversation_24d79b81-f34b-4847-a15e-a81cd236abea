package com.sourcecode

import android.content.Intent
import android.os.Bundle
import org.devio.rn.splashscreen.SplashScreen
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate
import com.facebook.react.modules.core.DeviceEventManagerModule


class MainActivity : ReactActivity() {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
   
   override fun onCreate(savedInstanceState: Bundle?) {
        SplashScreen.show(this)
        super.onCreate(savedInstanceState)
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        // Handle deep link from URL scheme
        val data = intent?.data
        if (data != null && data.scheme == "indiacustomercare") {
            sendDeepLinkToReactNative(data.toString())
            return
        }

        // Handle deep link from intent extras (for shortcuts)
        val deepLinkExtra = intent?.getStringExtra("deepLink")
        if (deepLinkExtra != null) {
            sendDeepLinkToReactNative(deepLinkExtra)
            return
        }
    }

    private fun sendDeepLinkToReactNative(deepLink: String) {
        val reactInstanceManager = reactNativeHost.reactInstanceManager
        val reactContext = reactInstanceManager.currentReactContext

        if (reactContext != null) {
            reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit("deepLink", deepLink)
        } else {
            // Store the deep link to send later when React Native is ready
            pendingDeepLink = deepLink
        }
    }

    private var pendingDeepLink: String? = null

    override fun onResume() {
        super.onResume()
        // Send pending deep link if React Native is now ready
        pendingDeepLink?.let { deepLink ->
            sendDeepLinkToReactNative(deepLink)
            pendingDeepLink = null
        }
    }

  override fun getMainComponentName(): String = "SourceCode"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)
}
