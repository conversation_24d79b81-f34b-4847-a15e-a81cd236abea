package com.sourcecode

import android.util.Log
import com.facebook.react.ReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.ViewManager

class AppShortcutPackage : ReactPackage {
    companion object {
        private const val TAG = "AppShortcutPackage"
    }

    override fun createNativeModules(reactContext: ReactApplicationContext): List<NativeModule> {
        Log.d(TAG, "Creating AppShortcutModule")
        val module = AppShortcutModule(reactContext)
        Log.d(TAG, "AppShortcutModule created: ${module.name}")
        return listOf(module)
    }

    override fun createViewManagers(reactContext: ReactApplicationContext): List<ViewManager<*, *>> {
        return emptyList()
    }
}
