package com.sourcecode

import android.content.Intent
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.graphics.drawable.Icon
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule

class AppShortcutModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    companion object {
        private const val TAG = "AppShortcutModule"
    }

    init {
        Log.d(TAG, "AppShortcutModule initialized")
    }

    override fun getName(): String {
        Log.d(TAG, "getName() called")
        return "AppShortcut"
    }

    @ReactMethod
    fun createShortcut(shortcutId: String, label: String, deepLink: String, promise: Promise) {
        Log.d(TAG, "createShortcut called with: id=$shortcutId, label=$label, deepLink=$deepLink")
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
                Log.d(TAG, "Using modern shortcut method (API ${Build.VERSION.SDK_INT})")
                createDynamicShortcut(shortcutId, label, deepLink, promise)
            } else {
                Log.d(TAG, "Using legacy shortcut method (API ${Build.VERSION.SDK_INT})")
                createLegacyShortcut(label, deepLink, promise)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in createShortcut", e)
            promise.reject("SHORTCUT_ERROR", "Failed to create shortcut: ${e.message}", e)
        }
    }

    @RequiresApi(Build.VERSION_CODES.N_MR1)
    private fun createDynamicShortcut(shortcutId: String, label: String, deepLink: String, promise: Promise) {
        Log.d(TAG, "createDynamicShortcut started")
        val activity = currentActivity
        if (activity == null) {
            Log.e(TAG, "No current activity available")
            promise.reject("NO_ACTIVITY", "No current activity available")
            return
        }
        Log.d(TAG, "Current activity: ${activity::class.java.simpleName}")

        val shortcutManager = activity.getSystemService(ShortcutManager::class.java)
        Log.d(TAG, "ShortcutManager obtained: $shortcutManager")

        if (shortcutManager?.isRequestPinShortcutSupported == true) {
            Log.d(TAG, "Pin shortcuts are supported")
            // Create intent that opens the main activity with deep link as URI data
            val intent = Intent(Intent.ACTION_VIEW, android.net.Uri.parse(deepLink)).apply {
                setClassName(activity.packageName, "com.sourcecode.MainActivity")
                addCategory(Intent.CATEGORY_DEFAULT)
                addCategory(Intent.CATEGORY_BROWSABLE)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            }
            Log.d(TAG, "Created intent with URI data: $deepLink")

            val shortcut = ShortcutInfo.Builder(activity, shortcutId)
                .setShortLabel(label)
                .setLongLabel("$label - India Customer Care")
                .setIcon(Icon.createWithResource(activity, R.mipmap.ic_launcher))
                .setIntent(intent)
                .build()

            try {
                Log.d(TAG, "Creating shortcut with label: $label")

                // Request to pin it directly (skip dynamic shortcuts for better pinning)
                Log.d(TAG, "Creating shortcut result intent")
                val callbackIntent = shortcutManager.createShortcutResultIntent(shortcut)
                val successCallback = android.app.PendingIntent.getBroadcast(
                    activity, shortcutId.hashCode(), callbackIntent,
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        android.app.PendingIntent.FLAG_IMMUTABLE or android.app.PendingIntent.FLAG_UPDATE_CURRENT
                    } else {
                        android.app.PendingIntent.FLAG_UPDATE_CURRENT
                    }
                )

                Log.d(TAG, "Requesting pin shortcut directly...")
                val result = shortcutManager.requestPinShortcut(shortcut, successCallback.intentSender)
                Log.d(TAG, "Pin shortcut request result: $result")

                if (result) {
                    promise.resolve("Shortcut pinning requested successfully. Look for confirmation dialog or check home screen.")
                } else {
                    promise.resolve("Shortcut request sent but may require user confirmation or launcher support.")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception in createDynamicShortcut", e)
                promise.reject("SHORTCUT_ERROR", "Failed to create shortcut: ${e.message}", e)
            }
        } else {
            Log.w(TAG, "Pin shortcuts not supported on this device")
            promise.reject("NOT_SUPPORTED", "Pin shortcuts not supported on this device")
        }
    }

    private fun createLegacyShortcut(label: String, deepLink: String, promise: Promise) {
        val activity = currentActivity
        if (activity == null) {
            promise.reject("NO_ACTIVITY", "No current activity available")
            return
        }

        val shortcutIntent = Intent(Intent.ACTION_VIEW).apply {
            data = android.net.Uri.parse(deepLink)
            setClassName(activity.packageName, "com.sourcecode.MainActivity")
            addCategory(Intent.CATEGORY_DEFAULT)
            addCategory(Intent.CATEGORY_BROWSABLE)
        }

        val addIntent = Intent().apply {
            putExtra(Intent.EXTRA_SHORTCUT_INTENT, shortcutIntent)
            putExtra(Intent.EXTRA_SHORTCUT_NAME, label)
            putExtra(Intent.EXTRA_SHORTCUT_ICON_RESOURCE,
                Intent.ShortcutIconResource.fromContext(activity, R.mipmap.ic_launcher))
            action = "com.android.launcher.action.INSTALL_SHORTCUT"
        }

        activity.sendBroadcast(addIntent)
        promise.resolve("Legacy shortcut created")
    }

    @ReactMethod
    fun removeShortcut(shortcutId: String, promise: Promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
                val activity = currentActivity
                if (activity == null) {
                    promise.reject("NO_ACTIVITY", "No current activity available")
                    return
                }

                val shortcutManager = activity.getSystemService(ShortcutManager::class.java)
                shortcutManager?.removeDynamicShortcuts(listOf(shortcutId))
                promise.resolve("Shortcut removed")
            } else {
                promise.reject("NOT_SUPPORTED", "Remove shortcuts not supported on this Android version")
            }
        } catch (e: Exception) {
            promise.reject("SHORTCUT_ERROR", "Failed to remove shortcut: ${e.message}", e)
        }
    }

    @ReactMethod
    fun isShortcutSupported(promise: Promise) {
        Log.d(TAG, "isShortcutSupported called")
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
                val activity = currentActivity
                if (activity == null) {
                    Log.d(TAG, "No current activity available")
                    promise.resolve(false)
                    return
                }

                val shortcutManager = activity.getSystemService(ShortcutManager::class.java)
                val supported = shortcutManager?.isRequestPinShortcutSupported ?: false
                Log.d(TAG, "Modern shortcuts supported: $supported")
                promise.resolve(supported)
            } else {
                // Legacy shortcuts are generally supported on older versions
                Log.d(TAG, "Using legacy shortcuts (API ${Build.VERSION.SDK_INT})")
                promise.resolve(true)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking shortcut support", e)
            promise.resolve(false)
        }
    }

    @ReactMethod
    fun getExistingShortcuts(promise: Promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
                val activity = currentActivity
                if (activity == null) {
                    promise.reject("NO_ACTIVITY", "No current activity available")
                    return
                }

                val shortcutManager = activity.getSystemService(ShortcutManager::class.java)
                val shortcuts = shortcutManager?.dynamicShortcuts ?: emptyList()
                
                val shortcutArray = Arguments.createArray()
                shortcuts.forEach { shortcut ->
                    val shortcutMap = Arguments.createMap().apply {
                        putString("id", shortcut.id)
                        putString("shortLabel", shortcut.shortLabel?.toString())
                        putString("longLabel", shortcut.longLabel?.toString())
                    }
                    shortcutArray.pushMap(shortcutMap)
                }
                
                promise.resolve(shortcutArray)
            } else {
                promise.resolve(Arguments.createArray())
            }
        } catch (e: Exception) {
            promise.reject("SHORTCUT_ERROR", "Failed to get shortcuts: ${e.message}", e)
        }
    }
}
