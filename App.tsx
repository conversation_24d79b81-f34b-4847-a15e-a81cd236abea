import React, {useEffect, useRef} from 'react';
import {NavigationContainer} from '@react-navigation/native';
import SplashScreen from 'react-native-splash-screen';
import {store} from './src/components/redux/store';
import {Linking} from 'react-native';
import NavStack from './src/navigation/NavStack';
import Toast from 'react-native-toast-message';
import {Provider} from 'react-redux';
import backgroundSyncManager from './src/services/backgroundSyncManager';

// Import first launch testing utilities for development
if (__DEV__) {
  require('./src/utils/testFirstLaunch');
}

const App = () => {
  const navigationRef = useRef(null);

  useEffect(() => {
    // Hide splash screen immediately
    SplashScreen.hide();

    // Initialize background sync manager
    const initializeBackgroundSync = async () => {
      try {
        await backgroundSyncManager.initialize();
        console.log('[App] Background sync manager initialized');
      } catch (error) {
        console.error(
          '[App] Failed to initialize background sync manager:',
          error,
        );
      }
    };

    initializeBackgroundSync();

    // Handle deep links
    const handleDeepLink = (event: {url: string}) => {
      const url = event.url;
      console.log('[DeepLink] Deep link received:', url);

      if (url && navigationRef.current) {
        try {
          const parsedUrl = new URL(url);
          const screen = parsedUrl.pathname.replace('/', '');
          const companyId = parsedUrl.searchParams.get('companyId');
          const title = parsedUrl.searchParams.get('title');

          console.log('[DeepLink] Parsed screen:', screen);
          console.log('[DeepLink] Company ID:', companyId);
          console.log('[DeepLink] Title:', title);

          if (screen === 'companydetails') {
            console.log(
              '[DeepLink] Navigating to CompanyDetailsScreen with companyId:',
              companyId,
              'and title:',
              title,
            );

            // Use navigationRef to navigate
            navigationRef.current?.navigate('CompanyDetailsScreen', {
              title: title || '',
              companyId: companyId ? parseInt(companyId, 10) : undefined,
              fromHistory: false,
            });
          }
        } catch (error) {
          console.error('[DeepLink] Error parsing deep link URL:', error);
        }
      } else {
        console.log('[DeepLink] No URL or navigation not ready:', {
          hasUrl: !!url,
          hasNavigation: !!navigationRef.current,
        });
      }
    };

    // Add event listener for deep links
    const linkingSubscription = Linking.addEventListener('url', handleDeepLink);
    // Handle initial deep link if app is opened via shortcut
    console.log('[App] Checking for initial URL...');
    Linking.getInitialURL()
      .then(url => {
        console.log('[App] Initial URL:', url);
        if (url) {
          console.log('[App] Found initial URL, handling with delay...');
          // Add a small delay to ensure navigation is ready
          setTimeout(() => {
            handleDeepLink({url});
          }, 1000);
        } else {
          console.log('[App] No initial URL found');
        }
      })
      .catch(error => {
        console.error('[App] Error getting initial URL:', error);
      });

    // Cleanup on unmount
    return () => {
      backgroundSyncManager.cleanup();
      linkingSubscription.remove();
    };
  }, []);

  return (
    <Provider store={store}>
      <NavigationContainer ref={navigationRef}>
        <NavStack />
        <Toast />
      </NavigationContainer>
    </Provider>
  );
};

// Utility function to parse the deep link URL
const parseRouteFromUrl = (url: string) => {
  const [path, queryString] = url.split('?');
  const params = Object.fromEntries(new URLSearchParams(queryString));
  if (path.includes('companydetails')) {
    return {name: 'CompanyDetails', params};
  }
  return {name: 'Home', params: {}};
};

export default App;
